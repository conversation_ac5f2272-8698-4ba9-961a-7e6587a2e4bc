import pandas as pd
import fitz
import camelot
import re
import itertools
from nameparser import HumanName
from services.DateParser import get_date_exception, is_date_string
from services.DatabaseService import DatabaseService
import textwrap
from rapidfuzz import process, fuzz
from dotenv import load_dotenv
import os
from datetime import datetime
from main import airtanker_app
from PyPDF2 import PdfReader

load_dotenv()


def parse_dominion_data_from_pdf(df,
                        temp_pdf_path,
                        error_entry_id_counter,
                        file_name,
                        all_active_work_order_entries,
                        file_id,
                        selected_week_ending):

    customer_name = "Dominion Technologies Group"
    errors = []
    name_errors = []
    records = [] # This will store the records for easier manipulation later

    # TODO: Lets create a cached SERVICE to fetch this customer id
    # TODO: Lets also create a service to match employee names, we create a new one in every template parsing
    # \---------- For now we will call db directly ----------
    database_service = DatabaseService()
    database_service.connect()

    # TODO: Instead of fetching customers and employees, lets work with all_active_work_order_entries

    # all_active_work_order_entries = database_service.get_active_work_orders(selected_week_ending=selected_week_ending) and returns the following columns:
    # WorkOrderID, Status, StatusID, WorkOrderNumber, ProjectID, ProjectNumber, EmployeeID, FullName, Alias, CustomerID, CustomerName, StartDate, AnticipatedEndDate, Description, SiteSheetID, StageID

    # Given all this data:
    # - First we find the customer in the customers from db, so if customer not found in active orders we tell the user and exit early
    # - Then we match the customer to the customers from the all_active_work_order_entries
    #   - Extract the unique customer names and its IDs from all_active_work_order_entries and do the matching
    # - If found we filter the all_active_work_order_entries to only include the ones for this customer (If not found, return error)
    # - Then we try to match the name of the employee
    #   - Extract the unique employee names and its IDs from the filtered all_active_work_order_entries and do the matching (try all)
    # - If found we filter the all_active_work_order_entries to only include the ones for this employee (If not found, return error - 'Employee {name} not found for customer {customer_name} active orders')
    # - If more than one work order is found, return name error to allow user to select the correct one with the message 'Employee has more than one work order assigned. Select the correct one.'
    # Now we have the work order, customer and employee, we can proceed to parse the hours

    # We should keep the db calls? will it make it better to show information of errors to the user?

    db_customers = database_service.get_customers()
    db_employees = database_service.get_employee_names() # Cache employee names for future matching
    # /-------------------------------------------------------
    database_service.disconnect()

    # \------------ Customer match on db section -------------
    # First try an exact match
    customer_match = next(
        (c for c in db_customers
         if c["CustomerName"].strip().lower() == customer_name.lower()),
        None
    )
    if customer_match:
        customer_id = customer_match["CustomerID"]
    # Fallback to fuzzy match
    else:
        names = [c["CustomerName"] for c in db_customers]
        best_name, score, idx = process.extractOne(customer_name, names, scorer=fuzz.token_set_ratio)
        if score >= 80:  # tune your threshold
            customer_id = db_customers[idx]["CustomerID"]
        else:
            print(f"No customer match for “{customer_name}” (score={score})")
            # Return early with error: 'No Customer found for {customer_name}'
            error = {
                'ID': error_entry_id_counter,
                'FileName': file_name,
                "Message": f"No Customer found for {customer_name}",
                'ReportedProjectNumber': '',
                'WorkOrderEntry': {},
                'Employee': '',
                'EmployeeID': '',
                'Hours': []
            }
            errors.append(error)
            error_entry_id_counter += 1
            return errors, name_errors, error_entry_id_counter
    # /-------------------------------------------------

    # Filter all_active_work_order_entries to only include the ones for this customer
    customer_work_order_entries = [wo for wo in all_active_work_order_entries if wo["CustomerID"] == customer_id]

    # Dominion pdfs will show the information per employee per page, so we first need to get the employee name of that page
    doc = fitz.open(temp_pdf_path)

    for page_index in range(len(doc)): # page_index is 0-based, so we add 1 to get the page number
        page = doc[page_index]
        w, h = page.rect.width, page.rect.height

        # define a clip rect for the top-left quadrant
        clip = fitz.Rect(0, 0, w/2, h/2) # fitz uses (0,0) at top-left

        # extract only from that region
        header_text = page.get_text("text", clip=clip)

        # \------------------- Week Ending Check --------------------
        # Check if week ending is correct, if not, skip this page and notify user with an error
        week_match = re.search(
            r"Week Ending:\s*(\d{1,2}/\d{1,2}/\d{4})", header_text
        )
        week_ending_date = week_match.group(1) if week_match else None
        if week_ending_date and get_date_exception(week_ending_date) != get_date_exception(selected_week_ending):
            error = {
                'ID': error_entry_id_counter,
                'FileName': file_name,
                "Message": f"Page {page_index + 1} skipped due to week ending mismatch. Week ending in page is {week_ending_date} and selected week ending is {selected_week_ending}.",
                'ReportedProjectNumber': '',
                'WorkOrderEntry': {},
                'Employee': '',
                'EmployeeID': '',
                'Hours': []
            }
            errors.append(error)
            error_entry_id_counter += 1
            # database_service.delete_data_with_fileID(file_id) # TODO: We should delete the page, not the entire file.
            continue
        # /----------------------------------------------------------

        # First try to match employee with the customer_work_order_entries, then as fallback try to find the employee in db and show user: 'Employee {name} not found for customer {customer_name} active orders.'
        # If user still not found in db, show user: 'Employee {name} not found in database... yadi yada' 
        name_match = re.search(r"Name:\s*([^\r\n]+)", header_text)
        name = name_match.group(1).strip() if name_match else ""
        employee_id = match_employee_name_to_id(name, customer_work_order_entries, db_employees, error_entry_id_counter, file_name, name_errors, errors) # Should we move it down? and after the workorder match

        if employee_id is None:
            continue # If no employee id, skip to next page

        # TODO: Add here the work order match, so we can pass it to the match_employee_name_to_id function

        # Do the work order match
        # We need to filter the customer_work_order_entries with the EmployeeID
        work_order_id = None
        project_id = None
        employee_work_order_entries = [wo for wo in customer_work_order_entries if wo["EmployeeID"] == employee_id]
        if len(employee_work_order_entries) > 1:
            print(f"Employee {name} has more than one work order assigned.") # Show user multiple workorders to select from
        elif len(employee_work_order_entries) == 0:
            print(f"Employee {name} has no work order assigned.")
        else:
            work_order_id = employee_work_order_entries[0]["WorkOrderID"]
            project_id = employee_work_order_entries[0]["ProjectID"]
        

        # now pull your table from the full page
        tables = camelot.read_pdf(
            temp_pdf_path,
            pages=str(page_index+1),
            flavor="stream",
            multiple_tables=False,
        )

        if not tables:
            airtanker_app.logger.error(f"No table found on page {page_index + 1}")
            continue

        page_df = tables[0].df
        daily_hours = extract_daily_hours(page_df)
 
        for date_str, hours in daily_hours.items():
            # build the raw dict and use the same fieldnames as in the database
            records.append({
                "Date": get_date_exception(date_str),
                "CustomerID": customer_id,
                "LocationID": None,
                "ProjectID": project_id,
                "TaskID": None,
                "CustomerReportedHours": float(hours),
                "FileID": file_id,
                "RateTypeID": None,
                "WorkOrderID": work_order_id,
                "EmployeeID": employee_id,
            })

    print(f"Records: \n{records}")

    # Insert the records into the database
    try:
        for record in records:
            database_service.insert_customer_reported_hours_lastRow(employee_id=record["EmployeeID"], 
                                                                    date=record["Date"], 
                                                                    customer_reported_hours=record["CustomerReportedHours"],
                                                                    project_id=record["ProjectID"],
                                                                    customer_id=record["CustomerID"],
                                                                    file_id=record["FileID"],
                                                                    work_order_id=record["WorkOrderID"],
                                                                    task_id=record["TaskID"],
                                                                    location_id=record["LocationID"],
                                                                    rate_type_id=record["RateTypeID"]);
    except Exception as e:
        airtanker_app.logger.error(f"Error inserting records into database: {e}")

    
    # error = {
    #     'ID': error_entry_id_counter,
    #     'FileName': file_name,
    #     "Message": "Dominion PDF format is not supported yet. This is a test implementation.",
    #     'ReportedProjectNumber': '',
    #     'WorkOrderEntry': {},
    #     'Employee': '',
    #     'EmployeeID': '',
    #     'Hours': []
    # }
    # errors.append(error)
    # error_entry_id_counter += 1
    
    # Return early for testing
    return errors, name_errors, error_entry_id_counter

def match_employee_name_to_id(
    name: str,
    customer_work_order_entries: list[dict],
    employees: dict[str, str],
    error_entry_id_counter: int,
    file_name: str,
    name_errors: list[dict] = None,
    errors: list[dict] = None,
) -> str | None:
    """
    Try to resolve `name` (from the PDF) to an EmployeeID in `customer_work_order_entries`.

    employees: mapping EmployeeID -> FullName
    name_errors: if provided, will be appended with info on any ambiguous / failed matches

    Returns: matched EmployeeID, or None if ambiguous / not found.
    """
    normalized_input = normalize_name(name)
    threshold_fuzzy = 90

    # Build map: normalized full_name -> list of EmployeeIDs
    norm_to_ids: dict[str, list[str]] = {}
    for emp_id, full_name in employees.items():
        norm_full = normalize_name(full_name)
        norm_to_ids.setdefault(norm_full, []).append(emp_id)

    # Build map: normalized full_name -> list of EmployeeIDs but from the customer_work_order_entries
    norm_to_ids_from_work_orders: dict[str, list[str]] = {}
    for entry in customer_work_order_entries:
        norm_full = normalize_name(entry["FullName"])
        norm_to_ids_from_work_orders.setdefault(norm_full, []).append(entry["EmployeeID"])

    # 1) Try exact normalized match from the customer_work_order_entries
    print(f"Trying exact match for {name} in customer_work_order_entries")
    if normalized_input in norm_to_ids_from_work_orders:
        ids = norm_to_ids_from_work_orders[normalized_input]
        if len(ids) == 1:
            print(f"Exact match for {name}: {ids[0]}")
            return ids[0]
    
    # 2) If no exact match, try fuzzy match from the customer_work_order_entries
    print(f"Trying fuzzy match for {name} in customer_work_order_entries")
    choices = list(norm_to_ids_from_work_orders.keys())
    best_name, score, _ = process.extractOne(normalized_input, choices, scorer=fuzz.token_set_ratio)
    if score >= threshold_fuzzy:
        ids = norm_to_ids_from_work_orders[best_name]
        if len(ids) == 1:
            print(f"Fuzzy match for {name}: {best_name} - {ids[0]} - {score}")
            return ids[0]
    
    # 3) Low-confidence suggestions (50 < score < threshold_fuzzy)
    if 50 < score < threshold_fuzzy:
        print(f"Low-confidence match for {name}: {best_name}")
        suggestions = []
        # grab the top 3 normalized names from the work orders
        for match_name, match_score, _ in process.extract(
            normalized_input,
            choices,
            scorer=fuzz.token_set_ratio,
            limit=3,
        ):
            # all the work-order IDs that had this normalized name
            work_order_ids = [
                entry.get("WorkOrderID")
                for entry in customer_work_order_entries
                if normalize_name(entry["FullName"]) == match_name
            ]
            # for each employeeID under that name
            for emp_id in norm_to_ids_from_work_orders[match_name]:
                suggestions.append({
                    "EmployeeName": employees.get(emp_id, match_name),
                    "EmployeeID": emp_id,
                    "WorkOrders": work_order_ids,
                })
        if name_errors is not None:
            name_errors.append({
                "ID": error_entry_id_counter,
                "OriginalName": name,
                "FileName": file_name,
                "Message": (
                    f"Original Name: <strong>{name}</strong>. "
                    "No confident match in the customer work orders. "
                    "Please select the correct employee below."
                ),
                "EmployeeData": suggestions,
                "Hours": [],
            })
        return None
                
    # If we reach this point, we have no match in the customer_work_order_entries, so we try to match against all employees and return an error depending on the results:
    #     If employee found on db but not in the customer_work_order_entries -> 'Employee {name} ({employee_id}) not found in customer {customer_name} active orders.'
    #     If employee not found even on db -> 'Employee {name} not found in database. Is he an active employee?'

    # 1) Try exact normalized match
    print(f"Trying exact match for {name}")
    if normalized_input in norm_to_ids:
        ids = norm_to_ids[normalized_input]
        if len(ids) == 1:
            print(f"Exact match for {name}: {ids[0]}")
            print(f"Employee {name} ({ids[0]}) not found in customer Dominion Technologies Group active orders.")
            error = {
                'ID': error_entry_id_counter,
                'FileName': file_name,
                "Message": f"Employee {name} ({ids[0]}) not found in customer Dominion Technologies Group active orders.",
                'ReportedProjectNumber': '',
                'WorkOrderEntry': {},
                'Employee': name,
                'EmployeeID': ids[0],
                'Hours': []
            }
            errors.append(error)
            error_entry_id_counter += 1

            return None

    # 2) Fuzzy match
    print(f"Trying fuzzy match for {name}")
    choices = list(norm_to_ids.keys())
    best_name, score, _ = process.extractOne(
        normalized_input, choices, scorer=fuzz.token_set_ratio
    )
    if score >= threshold_fuzzy:
        ids = norm_to_ids[best_name]
        if len(ids) == 1:
            print(f"Fuzzy match for {name}: {best_name} - {ids[0]} - {score}")
            print(f"Employee {name} ({ids[0]}) not found in customer Dominion Technologies Group active orders.")
            error = {
                'ID': error_entry_id_counter,
                'FileName': file_name,
                "Message": f"Employee {name} ({ids[0]}) not found in customer Dominion Technologies Group active orders.",
                'ReportedProjectNumber': '',
                'WorkOrderEntry': {},
                'Employee': name,
                'EmployeeID': ids[0],
                'Hours': []
            }
            errors.append(error)
            error_entry_id_counter += 1

            return None

    # 3) Here we can add a hole new Fallback logic

    # 4) No match at all
    print(f"No match for {name} from {file_name}")
    if errors is not None:
        error = {
            'ID': error_entry_id_counter,
            'FileName': file_name,
            "Message": f"Employee {name} not found in database. Is he an active employee?",
            'ReportedProjectNumber': '',
            'WorkOrderEntry': {},
            'Employee': name,
            'EmployeeID': '',
            'Hours': []
        }
        errors.append(error)
        error_entry_id_counter += 1

    return None

def normalize_name(name: str) -> str:
    name = name.strip().lower()
    # name = name = re.sub(r"[^\w\s]", "", name) # Remove non-alphanumeric characters
    name = re.sub(r"\s+", " ", name) # Remove multiple spaces
    return name.strip().lower()


def extract_daily_hours(page_df):
    """
    Extract dates and total hours from the DataFrame.
    Returns a dictionary with date as key and total hours as value.
    """

    # Date is column idx 0
    # Total Hours is column idx 8

    # Given that the table always comes like this... we have a 'format':
    # Date | Facility | Work Center | Equipment | Job Number | Time In | Lunch Hrs. | Time Out | Total Hrs.

    # The consistent format returned is this one:
    # Starting right in the next line after the header, we have a block of 5 rows where the first two  have information
    # Third one has the date of that day, have the date itself in a format like: MM/DD/YYYY
    # and last two (4 and 5) have more data

    # We will sum the hours from each day

    daily_hours = {}
    
    # Date pattern to match MM/DD/YY format
    date_pattern = r'^\d{2}/\d{2}/\d{2}$'
    
    # Find all rows that contain dates
    date_rows = []
    for idx, row in page_df.iterrows():
        date_value = str(row.iloc[0]).strip()  # Date is in column 0
        if re.match(date_pattern, date_value):
            date_rows.append(idx)
    
    # For each date, sum the hours from the 5-row block (2 above, date row, 2 below)
    for date_row_idx in date_rows:
        date = str(page_df.iloc[date_row_idx, 0]).strip()
        
        # Define the 5-row block around this date
        start_idx = date_row_idx - 2
        end_idx = date_row_idx + 2
        
        total_hours = 0.0
        
        # Sum hours from the 5-row block
        for i in range(start_idx, end_idx + 1):
            if 0 <= i < len(page_df):  # Make sure we're within bounds
                hours_value = str(page_df.iloc[i, 8]).strip()  # Total Hrs is column 8
                try:
                    if hours_value and hours_value != '' and hours_value != 'nan':
                        total_hours += float(hours_value)
                except (ValueError, TypeError):
                    # Skip invalid values
                    continue
        
        if total_hours > 0:
            daily_hours[date] = total_hours

    return daily_hours